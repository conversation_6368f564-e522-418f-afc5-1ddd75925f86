FROM alpine:3
WORKDIR /workspace

RUN apk update \
 && apk add --no-cache \
      ca-certificates
      ca-certificates aws-cli

# EG local zscalar certs
# you need to download the ZscalerRootCA.pem and store in the loki repo
# follow this instructions:https://expediagroup.atlassian.net/wiki/spaces/~a<PERSON><PERSON><PERSON><PERSON>/pages/222988430/Zscaler+cert+fix+for+Docker
COPY ZscalerRootCA.pem /usr/local/share/ca-certificates/

COPY . ./
