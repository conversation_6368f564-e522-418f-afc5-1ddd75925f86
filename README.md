# loki
Loki is the unified marketing bidding platform. This repo hosts the loki gRPC server &amp; loki platform utilities.


## Deployment

### Prod

[![Status](https://argo-cd.marketing.expedia.com/api/badge?name=app-loki)](https://argo-cd.marketing.expedia.com/applications/app-loki)

`URL` : `loki-server.marketing.expedia.com:443` . This is
configured [here](https://github.expedia.biz/gmo-performance-marketing/applications/blob/main/prod/loki/server/ingress-patch.json#L5)

### Test

[![Status](https://argo-cd.test.marketing.expedia.com/api/badge?name=app-loki)](https://argo-cd.test.marketing.expedia.com/applications/app-loki)

`URL` : `loki-server.test.marketing.expedia.com:443` . This is
configured [here](https://github.expedia.biz/gmo-performance-marketing/applications/blob/main/test/loki/server/ingress-patch.json#L5)

## Local setup

* Go [setup](**********************:gmo-performance-marketing/loki.git)
* Install dependencies: `go get <package name | package git url>`
* See [this](https://github.expedia.biz/gmo-performance-marketing/docs/blob/main/dev/grpc/calling-grpc-from-local.md) for instructions to query gRPC servers.
* Local development can use the debug server at http://127.0.0.1:8080/docs/ to test APIs.
* More docs TBD


## Logging

Logs can be queries in splunk with the following query
```
index="eg-sem-marketing-eks" splunk_server_group="lod" source="*loki-server*" | spath service | search
service=loki-assembler
```
