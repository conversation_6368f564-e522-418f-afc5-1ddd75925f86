package job

import (
	"fmt"

	"github.com/Netflix/go-env"
)

// config holds the configuration for the bid unit job
type config struct {
	LokiSvcAddr           string `env:"LOKI_SERVICE_ADDR,default=loki-server.test.marketing.expedia.com:443"`
	AwsRegion             string `env:"AWS_REGION,default=us-east-1"`
	Env                   string `env:"ENV,default=DEV"`
	StructureFilesS3Path  string `env:"STRUCTURE_FILES_S3_PATH,default=s3://eg-marketing-platform-prod/structure"`
	BidUnitS3Path         string `env:"BID_UNITS_S3_PATH,default=s3://eg-marketing-platform-prod/loki/bid-units"`
	WorkspaceDir          string `env:"WORKSPACE_DIR,default=./workspace"`
	StructureQuerySvcAddr string `env:"STRUCTURE_QUERY_SERVICE_ADDR,default=structure-query-svc.marketing.expedia.com:443"`
	OdinSvcAddr           string `env:"ODIN_SERVICE_ADDR,default=sem-misc-go.marketing.expedia.com:443"`
	SlackChannel          string `env:"SLACK_CHANNEL,default=#eg-mtt-loki-alerts"`
	TrackingUrl           string `env:"TRACKING_URL,default=https://argo-workflows.test.marketing.expedia.com/workflows/workflows"`
	MaxWorkers            int    `env:"MAX_WORKERS,default=5"`
	RequestTimeout        int    `env:"REQUEST_TIMEOUT,default=30"` // in minutes
}

// validate checks if the configuration is valid
func (c *config) validate() error {
	if c.LokiSvcAddr == "" {
		return fmt.Errorf("LOKI_SERVICE_ADDR is required")
	}
	if c.AwsRegion == "" {
		return fmt.Errorf("AWS_REGION is required")
	}
	if c.StructureFilesS3Path == "" {
		return fmt.Errorf("STRUCTURE_FILES_S3_PATH is required")
	}
	if c.BidUnitS3Path == "" {
		return fmt.Errorf("BID_UNITS_S3_PATH is required")
	}
	if c.WorkspaceDir == "" {
		return fmt.Errorf("WORKSPACE_DIR is required")
	}
	if c.StructureQuerySvcAddr == "" {
		return fmt.Errorf("STRUCTURE_QUERY_SERVICE_ADDR is required")
	}
	if c.OdinSvcAddr == "" {
		return fmt.Errorf("ODIN_SERVICE_ADDR is required")
	}
	if c.MaxWorkers <= 0 {
		c.MaxWorkers = 5 // Default to 5 workers if invalid value
	}
	if c.RequestTimeout <= 0 {
		c.RequestTimeout = 30 // Default to 30 minutes if invalid value
	}
	return nil
}

// GetConfig loads the configuration from environment variables and validates it
func GetConfig() (*config, error) {
	var cfg config

	_, err := env.UnmarshalFromEnviron(&cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal bid unit job config: %w", err)
	}

	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &cfg, nil
}
