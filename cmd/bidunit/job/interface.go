// Package job provides functionality for generating and managing bid units
package job

import (
	"fmt"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
)

// LokiBidUnit represents a bid unit in the Loki system
type LokiBidUnit struct {
	Id               string            // Unique identifier for the bid unit
	Labels           map[string]string // Metadata labels associated with the bid unit
	MarketingChannel string            // Marketing channel this bid unit belongs to
	Name             string            // Human-readable name for the bid unit
	Status           LokiBidUnitStatus // Current status of the bid unit
}

// LokiBidUnitStatus represents the status of a bid unit
type LokiBidUnitStatus int64

// Status constants for bid units
const (
	UNSPECIFIED LokiBidUnitStatus = iota
	ENABLED                       // Bid unit is active
	DISABLED                      // Bid unit is inactive
)

// Interface defines the contract for bid unit job implementations
type Interface interface {
	// GenerateBidUnits generates bid units and sends them to the provided channel
	GenerateBidUnits(inChan chan<- LokiBidUnit) error

	// MarketingChannel returns the marketing channel string for this job
	MarketingChannel() string
}

// SupportedChannels is a list of marketing channels supported by the bid unit job
var SupportedChannels = []string{
	"google-hotel-ads",
	// Add more supported channels here as they are implemented
}

// IsChannelSupported checks if a given marketing channel is supported
func IsChannelSupported(channel string) bool {
	channel = strings.ToLower(strings.TrimSpace(channel))
	for _, supported := range SupportedChannels {
		if channel == supported {
			return true
		}
	}
	return false
}

// GetBidUnitJob creates and returns a bid unit job implementation for the specified channel
func GetBidUnitJob(channel string, cfg *config, logger zerolog.Logger, awsClient aws.Client, bidUnitClient client.BidUnitClient) (Interface, error) {
	if channel == "" {
		return nil, fmt.Errorf("marketing channel cannot be empty")
	}

	channel = strings.ToLower(strings.TrimSpace(channel))

	if !IsChannelSupported(channel) {
		return nil, fmt.Errorf("marketing channel '%s' is not supported (supported channels: %s)",
			channel, strings.Join(SupportedChannels, ", "))
	}

	switch channel {
	case "google-hotel-ads":
		job, err := getGhaBidUnitJob(cfg, logger, awsClient, bidUnitClient)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize Google Hotel Ads bid unit job: %w", err)
		}
		return job, nil
	default:
		// This should never happen due to the IsChannelSupported check above
		return nil, fmt.Errorf("channel '%s' not supported", channel)
	}
}
