services:
  db:
    image: postgres
    environment:
      POSTGRES_PASSWORD: "${DB_PASSWORD:-postgres}"
    command:
      - '-c'
      - log_statement=ddl
    ports:
      - "6432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 1s
      retries: 10

  server:
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      db:
        condition: service_healthy
    volumes:
    - ./bin:/workspace
    - $HOME/.aws:/root/.aws:ro
    ports:
    - "50051:50051"
    - "9090:9090"
    - "8080:8080"
    command: /workspace/server
    environment:
      ENV: dev
      LOG_LEVEL: 0
      DB_HOST: db
      DB_USER: postgres
      DB_PASSWORD: postgres
      LOKI_SERVICE_ADDR: "localhost:50051"
      ODIN_SERVICE_ADDR: "localhost:50051"
