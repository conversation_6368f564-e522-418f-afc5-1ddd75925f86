package config

import (
	"fmt"

	"github.com/Netflix/go-env"
)

type BidAssemblyConfig struct {
	LokiSvcAddr string `env:"LOKI_SERVICE_ADDR,default=loki-server.marketing.expedia.com:443"`
	//TODO: change it loki namespace once created
	TrackingUrl      string `env:"TRACKING_URL,default=https://argo-workflows.test.marketing.expedia.com/workflows/workflows"`
	MarketLabSvcAddr string `env:"MARKETLAB_SERVICE_ADDR,default=https://marketlab-ui.rcp.us-east-1.marketing.prod.exp-aws.net/api/experiments"`
	AwsRegion        string `env:"AWS_REGION,default=us-east-1"`
	LokiS3Path       string `env:"S3_PATH,default=s3://eg-marketing-platform-prod/loki"`
	Env              string `env:"ENV,default=DEV"`
	WorkDir          string `env:"WORK_DIR,default=./workspace"`
}

func GetBidAssemblyConfig() (*BidAssemblyConfig, error) {
	var config BidAssemblyConfig

	_, err := env.UnmarshalFromEnviron(&config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal bidassembly config: %w", err)
	}
	return &config, nil
}
