package config

import (
	"fmt"

	"github.com/Netflix/go-env"
)

type RunHandlerConfig struct {
	LokiSvcAddr string `env:"LOKI_SERVICE_ADDR,default=loki-server.marketing.expedia.com:443"`
	Env         string `env:"ENV,default=DEV"`
}

func GetRunHandlerConfig() (*RunHandlerConfig, error) {
	var config RunHandlerConfig

	_, err := env.UnmarshalFromEnviron(&config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal assembler config: %w", err)
	}
	return &config, nil
}
