package service

import (
	"fmt"

	env "github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	libgrpc "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

func RegisterService(s grpc.ServiceRegistrar, orm *gorm.DB, logger zerolog.Logger) error {
	svc, err := newService(orm, logger)
	if err != nil {
		return err
	}

	pb.RegisterBidUnitServiceServer(s, svc)
	logger.Info().Msg("registered bidunit service")
	return nil
}

type serviceConfig struct {
	LokiSvcAddr string `env:"LOKI_SERVICE_ADDR,default:localhost:50051"`
}

type service struct {
	pb.UnimplementedBidUnitServiceServer

	cfg         serviceConfig
	orm         *gorm.DB
	logger      zerolog.Logger
	ruleClient  pb.RuleServiceClient
	modelClient pb.ModelServiceClient
}

func newService(orm *gorm.DB, logger zerolog.Logger) (*service, error) {
	var svcCfg serviceConfig
	_, err := env.UnmarshalFromEnviron(&svcCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}

	err = orm.AutoMigrate(&BidUnit{}, &BidUnitConfig{})
	if err != nil {
		return nil, fmt.Errorf("autoMigrate: %w", err)
	}
	lokiConn, err := libgrpc.DialWithRetry(svcCfg.LokiSvcAddr, []string{"loki.v1.RuleService", "loki.v1.ModelService"})
	if err != nil {
		return nil, fmt.Errorf("dial loki: %w", err)
	}

	return &service{
		cfg:         svcCfg,
		orm:         orm,
		logger:      logger.With().Str("service", "loki.bidunit").Logger(),
		modelClient: pb.NewModelServiceClient(lokiConn),
		ruleClient:  pb.NewRuleServiceClient(lokiConn),
	}, nil
}
