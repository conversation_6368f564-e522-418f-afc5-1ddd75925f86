package model

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/lib/pq"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func (s *service) GetModels(
	_ context.Context, req *pb.GetModelsRequest,
) (*pb.GetModelsResponse, error) {
	query := s.orm

	if req.MarketingChannel != pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		query = query.Where("marketing_channels && ?", pq.Array([]int32{int32(req.MarketingChannel)}))
	}
	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
		}
		query = query.Where("id = ?", id)
	}
	if req.Name != "" {
		query = query.Where("name = ?", req.Name)
	}
	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}
	if req.ContactDl != "" {
		query = query.Where("contact_dls && ?", pq.Array([]string{req.ContactDl}))
	}
	for labelKey, labelValue := range req.Labels {
		query = query.Where("labels->>? = ?", labelKey, labelValue)
	}
	if len(req.Statuses) > 0 {
		query = query.Where("status IN (?)", req.Statuses)
	}
	if len(req.RuleSchemaIds) > 0 {
		query = query.Where("rule_schema_ids <@ ?", pq.Array(req.RuleSchemaIds))
	}
	if len(req.OrchestratorTypes) > 0 {
		query = query.Where("orchestrator_type in (?)", req.OrchestratorTypes)
	}
	if req.CreatedBy != "" {
		query = query.Where("created_by = ?", req.CreatedBy)
	}

	var models []*Model
	err := query.Order("id desc").Find(&models).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getModelsFromDB: %v", err)
	}
	pbModels := make([]*pb.Model, len(models))
	for i, model := range models {
		m, err := model.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbModels[i] = m
	}
	return &pb.GetModelsResponse{Models: pbModels}, nil
}

func (s *service) CreateModel(
	ctx context.Context, req *pb.CreateModelRequest,
) (*pb.CreateModelResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "`name` is required")
	}
	if req.Version == "" {
		return nil, status.Error(codes.InvalidArgument, "`version` is required")
	}
	if len(req.MarketingChannels) == 0 {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channels` is required")
	}
	if req.OrchestratorConfig.Type == pb.OrchestratorType_ORCHESTRATOR_TYPE_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`orchestrator_config.type` is required")
	}

	orchestratorConfig, err := proto.Marshal(req.OrchestratorConfig)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalOrchestratorExecContext: %v", err)
	}

	if err = s.modelValidations(req.Name, req.RunParametersJsonSchema, req.RuleSchemaIds); err != nil {
		return nil, err
	}

	model := &Model{
		Name:                    req.Name,
		Version:                 req.Version,
		Description:             req.Description,
		RunParametersJsonSchema: req.RunParametersJsonSchema,
		MarketingChannels:       marketingChannelsEnumListToInt32List(req.MarketingChannels),
		ContactDls:              req.ContactDls,
		Labels:                  req.Labels,
		Status:                  int32(pb.ModelStatus_MODEL_STATUS_ENABLED),
		RuleSchemaIds:           req.RuleSchemaIds,
		OrchestratorType:        int32(req.OrchestratorConfig.Type),
		OrchestratorConfig:      orchestratorConfig,
		CreatedBy:               user,
	}

	if err := s.orm.Create(model).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "createModelInDB: %v", err)
	}
	return &pb.CreateModelResponse{Id: strconv.FormatUint(model.Id, 10)}, nil
}

func (s *service) UpdateModel(
	ctx context.Context, req *pb.UpdateModelRequest,
) (*pb.UpdateModelResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is required")
	}
	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
	}
	if err = s.modelValidations(req.Id, req.RunParametersJsonSchema, req.RuleSchemaIds); err != nil {
		return nil, err
	}

	model := &Model{
		Id:                      id,
		Description:             req.Description,
		RunParametersJsonSchema: req.RunParametersJsonSchema,
		MarketingChannels:       marketingChannelsEnumListToInt32List(req.MarketingChannels),
		ContactDls:              req.ContactDls,
		Labels:                  req.Labels,
		Status:                  int32(req.Status),
		RuleSchemaIds:           req.RuleSchemaIds,
		UpdatedBy:               user,
	}
	if req.OrchestratorConfig != nil {
		model.OrchestratorConfig, _ = proto.Marshal(req.OrchestratorConfig)
	}

	err = s.orm.Updates(&model).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateModelInDB: %v", err)
	}
	return &pb.UpdateModelResponse{}, nil
}

func (s *service) GetModelRuns(
	_ context.Context, req *pb.GetModelRunsRequest,
) (*pb.GetModelRunsResponse, error) {
	query := s.orm
	if req.Id != "" {
		modelRunId, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", modelRunId)
	}
	if req.ModelId != "" {
		modelId, err := strconv.ParseUint(req.ModelId, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "model_id: %v is invalid", req.ModelId)
		}
		query = query.Where("model_id = ?", modelId)
	}
	if req.StartedBefore.IsValid() {
		query = query.Where("started_at <= ?", req.StartedBefore.AsTime())
	}
	if req.StartedAfter.IsValid() {
		query = query.Where("started_at >= ?", req.StartedAfter.AsTime())
	}
	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}

	var modelRuns []*ModelRun
	err := query.Find(&modelRuns).Limit(limit).Offset(offset).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getModelRunsFromDB: %v", err)
	}

	pbModelRuns := make([]*pb.ModelRun, len(modelRuns))
	for i, modelRun := range modelRuns {
		mr, err := modelRun.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbModelRuns[i] = mr
	}
	return &pb.GetModelRunsResponse{Runs: pbModelRuns}, nil
}

func (s *service) CreateModelRun(
	_ context.Context, req *pb.CreateModelRunRequest,
) (*pb.CreateModelRunResponse, error) {
	if req.ModelId == "" {
		return nil, status.Error(codes.InvalidArgument, "`model_id` is required")
	}
	id, err := strconv.ParseUint(req.ModelId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "model_id: %v is not valid", req.ModelId)
	}

	orchestratorExecContext, _ := proto.Marshal(&pb.OrchestratorExecContext{
		ExecutionStatus: pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_QUEUED,
	})

	pipelineRunId, _ := strconv.ParseUint(req.PipelineRunId, 10, 64)
	m := &ModelRun{
		ModelId:                 id,
		RunParameters:           req.RunParameters,
		PipelineRunId:           pipelineRunId,
		OrchestratorExecContext: orchestratorExecContext,
		Labels:                  req.Labels,
		CreatedBy:               req.CreatedBy,
	}

	if err := s.orm.Create(m).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "createModelRunRequestInDB: %v", err)
	}
	return &pb.CreateModelRunResponse{Id: strconv.FormatUint(m.Id, 10)}, nil
}

func (s *service) UpdateModelRun(
	ctx context.Context, req *pb.UpdateModelRunRequest,
) (*pb.UpdateModelRunResponse, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is required")
	}
	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
	}

	modelRun := &ModelRun{Id: id}
	// Record found, update record
	if len(req.Outputs) > 0 {
		// TODO: check if bid unit marketing channel and model supports that marketing channel?
		outputs, err := protoserde.MarshalArray(req.Outputs)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalModelOutputs: %v", err)
		}
		modelRun.Outputs = outputs
	}
	if len(req.RuleItems) > 0 {
		ruleItems, err := protoserde.MarshalArray(req.RuleItems)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalRuleItems: %v", err)
		}
		modelRun.RuleItems = ruleItems
	}
	if req.OrchestratorExecContext != nil {
		orchestratorContext, err := proto.Marshal(req.OrchestratorExecContext)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalProto: %v", err)
		}
		modelRun.OrchestratorExecContext = orchestratorContext
	}
	if req.OrchestratorExecContext != nil && req.OrchestratorExecContext.ExecutionStatus == pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
		modelRun.FinishedAt = time.Now()
	}
	err = s.orm.Updates(&modelRun).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateModelRunRequestInDB: %v", err)
	}

	return &pb.UpdateModelRunResponse{}, nil
}

func (s *service) modelValidations(resource string, runParametersSchema string, schemaIds []string) error {
	if runParametersSchema != "" {
		s.logger.Info().Msgf("validating the json schema: %s", resource)
		err := s.schemaCompiler.AddResource(resource, strings.NewReader(runParametersSchema))
		if err != nil {
			return status.Errorf(codes.Internal, "unable to add `item_json_schema` : %v", err)
		}
		_, err = s.schemaCompiler.Compile(resource)
		if err != nil {
			return status.Errorf(codes.InvalidArgument, "json schema is invalid: %v", err)
		}
	}

	if len(schemaIds) > 0 {
		for _, ruleSchemaId := range schemaIds {
			resp, err := s.ruleClient.GetRuleSchemas(context.Background(), &pb.GetRuleSchemasRequest{Id: ruleSchemaId})
			if err != nil {
				return status.Errorf(codes.Internal, "rule schema: %s is invalid: %v", ruleSchemaId, err)
			}
			if len(resp.Schemas) == 0 {
				return status.Errorf(codes.NotFound, "rule schema: %s not found", ruleSchemaId)
			}
		}
	}
	return nil
}
