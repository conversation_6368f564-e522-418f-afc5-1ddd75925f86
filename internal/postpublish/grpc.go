package service

import (
	"context"
	"strconv"
	"time"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func (s *service) CreatePostPublishRun(
	ctx context.Context, req *pb.CreatePostPublishRunRequest,
) (*pb.CreatePostPublishRunResponse, error) {
	if req.PipelineRunId == "" {
		return nil, status.Error(codes.InvalidArgument, "`pipeline_run_id` is a required field")
	}

	orchestartorExecContext, err := proto.Marshal(&pb.OrchestratorExecContext{
		ExecutionStatus: pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_QUEUED,
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marhsalOrchestratorExecContext: %v", err)
	}
	ar := &PostPublishRun{
		PipelineRunId:           req.PipelineRunId,
		RunParameters:           req.RunParameters,
		OrchestratorExecContext: orchestartorExecContext,
	}

	err = s.orm.Create(ar).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreatePostPublishRunResponse{Id: strconv.FormatUint(ar.Id, 10)}, nil
}

func (s *service) GetPostPublishRuns(
	_ context.Context, req *pb.GetPostPublishRunsRequest,
) (*pb.GetPostPublishRunsResponse, error) {
	var postPublishRuns []*PostPublishRun
	query := s.orm

	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", id)
	}

	if req.StartedAfter != nil {
		query = query.Where("created_at >= ?", req.StartedAfter.AsTime())
	}
	if req.StartedBefore != nil {
		query = query.Where("created_at <= ?", req.StartedBefore.AsTime())
	}

	err := query.Find(&postPublishRuns).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbPostPublishRuns := make([]*pb.PostPublishRun, len(postPublishRuns))
	for i, postPublishRun := range postPublishRuns {
		pbPostPublishRun, err := postPublishRun.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbPostPublishRuns[i] = pbPostPublishRun
	}

	return &pb.GetPostPublishRunsResponse{Runs: pbPostPublishRuns}, nil
}

func (s *service) UpdatePostPublishRun(
	ctx context.Context, req *pb.UpdatePostPublishRunRequest,
) (*pb.UpdatePostPublishRunResponse, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
	}

	postPublishRun := &PostPublishRun{
		Id: id,
	}
	if req.OrchestratorExecContext != nil {
		postPublishRun.OrchestratorExecContext, err = proto.Marshal(req.OrchestratorExecContext)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marhsalOrchestratorExecContext: %v", err)
		}
	}
	if req.OrchestratorExecContext != nil && req.OrchestratorExecContext.ExecutionStatus == pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
		postPublishRun.FinishedAt = time.Now()
	}

	err = s.orm.Updates(&postPublishRun).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdatePostPublishRunResponse{}, nil
}
