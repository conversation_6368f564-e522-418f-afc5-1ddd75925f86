package service

import (
	"fmt"

	env "github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

func RegisterService(s grpc.ServiceRegistrar, orm *gorm.DB, logger zerolog.Logger) error {
	svc, err := newService(orm, logger)
	if err != nil {
		return err
	}

	pb.RegisterPostPublishServiceServer(s, svc)
	logger.Info().Msg("registered post publish service")
	return nil
}

type serviceConfig struct{}

type service struct {
	pb.UnimplementedPostPublishServiceServer

	cfg    serviceConfig
	orm    *gorm.DB
	logger zerolog.Logger
}

func newService(orm *gorm.DB, logger zerolog.Logger) (*service, error) {
	var svcCfg serviceConfig
	_, err := env.UnmarshalFromEnviron(&svcCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}

	err = orm.AutoMigrate(&PostPublishRun{})
	if err != nil {
		return nil, fmt.Errorf("autoMigrate: %w", err)
	}

	return &service{
		cfg:    svcCfg,
		logger: logger.With().Str("service", "loki.postpublish").Logger(),
		orm:    orm,
	}, nil
}
